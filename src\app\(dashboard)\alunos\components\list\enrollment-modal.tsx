'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Loader2, CreditCard, Clock, Users, CheckCircle2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { getActivePlans } from '@/app/(dashboard)/academia/actions/plan-actions';
import { createMembership } from '@/app/(dashboard)/academia/actions/membership-actions';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { cn } from '@/lib/utils';
import { Student } from '../../types';

interface Plan {
  id: string;
  title: string;
  plan_type: 'individual' | 'family' | 'corporate';
  pricing_config: {
    type: 'recurring' | 'one-time' | 'per-session' | 'trial';
    amount: number;
    frequency?: 'monthly' | 'weekly' | 'yearly';
    frequency_number?: number;
    currency?: string;
  };
  duration_config: {
    type: 'ongoing' | 'limited' | 'specific';
    value?: number;
    unit?: 'days' | 'months' | 'years';
  };
  access_config: {
    frequency?: string;
    quantity?: number;
    modalities?: string[];
  };
  benefits: string[];
}

interface EnrollmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  student: Student | null;
  onSuccess?: () => void;
}

export function EnrollmentModal({ isOpen, onClose, student, onSuccess }: EnrollmentModalProps) {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const [isLoadingPlans, setIsLoadingPlans] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carregar planos ativos quando o modal abrir
  useEffect(() => {
    if (isOpen) {
      loadActivePlans();
    }
  }, [isOpen]);

  // Limpar estado quando fechar o modal
  useEffect(() => {
    if (!isOpen) {
      setSelectedPlanId('');
      setError(null);
      setPlans([]);
    }
  }, [isOpen]);

  const loadActivePlans = async () => {
    try {
      setIsLoadingPlans(true);
      setError(null);

      const result = await getActivePlans();
      
      if (result.success && result.data) {
        // Garantir que result.data é um array de planos
        const plansData = Array.isArray(result.data) ? result.data : [];
        setPlans(plansData);
      } else {
        setError(result.errors?._form || 'Erro ao carregar planos');
      }
    } catch (error) {
      console.error('Erro ao carregar planos:', error);
      setError('Erro interno ao carregar planos');
    } finally {
      setIsLoadingPlans(false);
    }
  };

  const formatPrice = (pricing: Plan['pricing_config']) => {
    const amount = pricing.amount || 0;
    const currency = pricing.currency || 'BRL';
    
    const formattedAmount = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency === 'BRL' ? 'BRL' : 'USD',
    }).format(amount);

    if (pricing.type === 'recurring') {
      const frequency = pricing.frequency || 'monthly';
      const frequencyNumber = pricing.frequency_number || 1;
      
      let frequencyText = '';
      if (frequency === 'monthly') {
        frequencyText = frequencyNumber === 1 ? 'mês' : `${frequencyNumber} meses`;
      } else if (frequency === 'weekly') {
        frequencyText = frequencyNumber === 1 ? 'semana' : `${frequencyNumber} semanas`;
      } else if (frequency === 'yearly') {
        frequencyText = frequencyNumber === 1 ? 'ano' : `${frequencyNumber} anos`;
      }
      
      return `${formattedAmount}/${frequencyText}`;
    }
    
    return formattedAmount;
  };

  const getPlanTypeLabel = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Individual';
      case 'family':
        return 'Familiar';
      case 'corporate':
        return 'Corporativo';
      default:
        return type;
    }
  };

  const getPlanTypeIcon = (type: string) => {
    switch (type) {
      case 'individual':
        return <Users className="h-4 w-4 text-blue-500 dark:text-blue-400" />;
      case 'family':
        return <Users className="h-4 w-4 text-green-500 dark:text-green-400" />;
      case 'corporate':
        return <Users className="h-4 w-4 text-purple-500 dark:text-purple-400" />;
      default:
        return <CreditCard className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const handleEnroll = async () => {
    if (!selectedPlanId || !student) {
      toast.error('Selecione um plano para continuar');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Obter dados do usuário atual para pegar o tenant_id
      const user = await getCurrentUser();
      if (!user?.app_metadata?.tenant_id) {
        throw new Error('Usuário não autenticado ou tenant não identificado');
      }

      const result = await createMembership({
        alunoId: student.student_id, // Usar student_id da tabela students
        planoId: selectedPlanId,
        dataInicio: new Date().toISOString().split('T')[0], // Data atual
        metadata: {}
      }, user.app_metadata.tenant_id);

      if (result.success) {
        toast.success('Aluno matriculado com sucesso!');
        onSuccess?.();
        onClose();
      } else {
        const errorMessage = result.errors?._form || 'Erro ao matricular aluno';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Erro ao matricular aluno:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!student) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 tenant-primary" />
            Matricular Aluno em Plano
          </DialogTitle>
          <DialogDescription>
            Selecione um plano ativo para matricular <strong>{student.name}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-700 dark:text-red-400">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {isLoadingPlans ? (
            <div className="flex items-center justify-center py-8 text-muted-foreground">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Carregando planos...</span>
            </div>
          ) : plans.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="font-medium">Nenhum plano ativo encontrado</p>
              <p className="text-sm mt-1">Crie um plano ativo para poder matricular alunos</p>
            </div>
          ) : (
            <RadioGroup value={selectedPlanId} onValueChange={setSelectedPlanId}>
              <div className="space-y-3">
                {plans.map((plan) => (
                  <div key={plan.id} className="relative">
                    <RadioGroupItem
                      value={plan.id}
                      id={plan.id}
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor={plan.id}
                      className={cn(
                        "flex cursor-pointer rounded-lg border-2 p-4 transition-all duration-200",
                        "hover:shadow-sm dark:hover:shadow-slate-800/20",
                        selectedPlanId === plan.id
                          ? "bg-[var(--tenant-primary)]/5 dark:bg-[var(--tenant-primary)]/10 shadow-md tenant-primary-border"
                          : "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 peer-checked:bg-[var(--tenant-primary)]/5 dark:peer-checked:bg-[var(--tenant-primary)]/10 peer-checked:tenant-primary-border"
                      )}
                    >
                      <Card className="w-full border-0 shadow-none bg-transparent">
                        <CardContent className="p-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                {getPlanTypeIcon(plan.plan_type)}
                                <h3 className="font-semibold text-lg text-foreground">{plan.title}</h3>
                                <Badge variant="outline" className="text-xs border-muted-foreground/30">
                                  {getPlanTypeLabel(plan.plan_type)}
                                </Badge>
                              </div>
                              
                              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                                <div className="flex items-center gap-1">
                                  <CreditCard className="h-3 w-3 text-green-600 dark:text-green-400" />
                                  <span className="font-medium text-foreground/90">{formatPrice(plan.pricing_config)}</span>
                                </div>

                                {plan.duration_config.type !== 'ongoing' && (
                                  <div className="flex items-center gap-1">
                                    <Clock className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                                    <span>
                                      {plan.duration_config.type === 'limited'
                                        ? `${plan.duration_config.value} ${plan.duration_config.unit}`
                                        : 'Período específico'
                                      }
                                    </span>
                                  </div>
                                )}
                              </div>

                              {plan.benefits && plan.benefits.length > 0 && (
                                <div className="space-y-1">
                                  <p className="text-xs font-medium text-muted-foreground">Benefícios:</p>
                                  <ul className="text-xs text-muted-foreground space-y-1">
                                    {plan.benefits.slice(0, 3).map((benefit, index) => (
                                      <li key={index} className="flex items-center gap-1">
                                        <CheckCircle2 className="h-3 w-3 tenant-secondary" />
                                        <span className="text-foreground/80">{benefit}</span>
                                      </li>
                                    ))}
                                    {plan.benefits.length > 3 && (
                                      <li className="text-xs text-muted-foreground">
                                        +{plan.benefits.length - 3} benefícios adicionais
                                      </li>
                                    )}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t border-border">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="border-muted-foreground/20 hover:bg-muted/50"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleEnroll}
            disabled={!selectedPlanId || isSubmitting || plans.length === 0}
            className="tenant-primary-bg hover:bg-[var(--tenant-primary)]/90 text-white shadow-sm hover:shadow-md transition-all duration-200"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Matriculando...
              </>
            ) : (
              'Matricular Aluno'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
